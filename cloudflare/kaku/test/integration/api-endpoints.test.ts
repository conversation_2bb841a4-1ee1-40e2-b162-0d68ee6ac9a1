import { env, fetchMock } from 'cloudflare:test';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { CoordinatorDO } from '../../src/coordinator/coordinator-do';
import { PlatformTypes } from '../../src/ui/constants';
import app from '../../src/api/index';
import {
  CreateLinkResponse,
  ConnectionsResponse,
  Connection,
} from '../../src/shared/coordinator-types';

export const createUser = (username: string, password: string, userId: string) => {
  const basicToken = btoa(`${username}:${password}`);
  setupUserFetch(basicToken, { userId });
  return basicToken;
};

export const setupUserFetch = (requestedUserAuthKey: string, response: any, status = 200) => {
  const pingUserPath = `web/v1/users`;
  return fetchMock
    .get(env.SUNNY_API_ENDPOINT)
    .intercept({
      path: pingUserPath,
      headers: {
        Authorization: `Basic ${requestedUserAuthKey}`,
      },
      method: 'get',
    })
    .reply(status, response)
    .persist();
};

describe('API Endpoints Integration Tests', () => {
  let testCounter = 0;

  beforeEach(async () => {
    testCounter++;
    fetchMock.activate();
  });

  afterEach(async () => {
    fetchMock.deactivate();
  });

  describe('POST /links', () => {
    it('should create a new link successfully', async () => {
      // Arrange
      const platform: PlatformTypes = 'facebook';
      const userId = 'test-user-create';
      const requestBody = {
        serviceId: platform,
        userId: userId,
      };

      // Act
      const response = await app.request(
        '/links',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as CreateLinkResponse;
      expect(responseData).toBeDefined();
      expect(responseData.linkId).toBeDefined();
      expect(responseData.url).toBeDefined();
      expect(responseData.expiresAt).toBeDefined();

      // Verify URL format
      expect(responseData.url).toContain(responseData.linkId);
      expect(responseData.url).toContain(platform);
      expect(responseData.url).toContain(`userId=${userId}`);
    });

    it('should return 400 for invalid serviceId', async () => {
      // Arrange
      const requestBody = {
        serviceId: 'invalid-platform',
        userId: 'test-user',
      };

      // Act
      const response = await app.request(
        '/links',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        },
        env,
      );

      // Assert
      expect(response.status).toBe(400);

      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Invalid serviceId');
    });
  });

  describe('GET /:linkId/:serviceId/reset', () => {
    it('should reset a link successfully', async () => {
      // Arrange - Create a link first
      const serviceId: PlatformTypes = 'netflix';
      const userId = `test-user-reset-${testCounter}`;

      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const createResult = await coordinator.createLink(serviceId, userId);
      const linkId = createResult.linkId;

      // Act
      const response = await app.request(
        `/connections/${linkId}/${serviceId}/reset?userId=${userId}`,
        {
          method: 'GET',
        },
        env,
      );

      // Assert
      expect(response.status).toBe(302); // Redirect status

      // Check that the location header contains a new URL
      const location = response.headers.get('location');
      expect(location).toBeDefined();
      expect(location).toContain(serviceId);

      // Verify the original link is now expired
      const linkStatus = await coordinator.getStatus(linkId);
      expect(linkStatus?.status).toBe('expired');
    });

    it('should return 500 for non-existent link (invalid link middleware)', async () => {
      // Arrange
      const nonExistentLinkId = 'non-existent-link-id';
      const serviceId = 'facebook';
      const userId = 'test-user';

      // Act
      const response = await app.request(
        `/connections/${nonExistentLinkId}/${serviceId}/reset?userId=${userId}`,
        {
          method: 'GET',
        },
        env,
      );

      // Assert
      expect(response.status).toBe(500);

      const responseText = await response.text();
      expect(responseText).toContain('Invalid Link');
    });

    it('should handle retry limit exceeded error', async () => {
      // Arrange
      const serviceId: PlatformTypes = 'github';
      const userId = `test-user-retry-${testCounter}`;

      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const createResult = await coordinator.createLink(serviceId, userId);
      let currentLinkId = createResult.linkId;

      // retry attempts (PLATFORM_RETRY_LIMIT = 3)
      for (let i = 0; i < 3; i++) {
        const resetResult = await coordinator.resetLink(currentLinkId, userId);
        if (resetResult) {
          currentLinkId = resetResult.linkInfo.url.split('/')[4];
        }
      }

      // Act - Try to reset again (should exceed limit)
      const response = await app.request(
        `/connections/${currentLinkId}/${serviceId}/reset?userId=${userId}`,
        {
          method: 'GET',
        },
        env,
      );

      // Assert
      expect(response.status).toBe(429); // Too Many Requests

      const responseText = await response.text();
      expect(responseText).toContain('Maximum Retry Attempts Reached');
      expect(responseText).toContain('retry attempts (3)');
    });
  });

  describe('GET /connections', () => {
    it('should return platform connections for authenticated user', async () => {
      // Arrange
      const userId = `test-user-connections-${testCounter}`;
      const username = 'testuser';
      const password = 'testpass';

      const basicToken = createUser(username, password, userId);

      // Act
      const response = await app.request(
        '/connections',
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as ConnectionsResponse;
      expect(responseData).toBeDefined();
      expect(responseData.connections).toBeDefined();
      expect(Array.isArray(responseData.connections)).toBe(true);
      expect(responseData.connections.length).toBeGreaterThan(0);

      // Verify each connection has required properties
      responseData.connections.forEach((connection) => {
        expect(connection.id).toBeDefined();
        expect(connection.name).toBeDefined();
        expect(connection.logo).toBeDefined();
        expect(typeof connection.connected).toBe('boolean');
        expect(connection.connected).toBe(false); // Should be false initially
      });

      // Verify specific platforms are included
      const platformIds = responseData.connections.map((c) => c.id);
      expect(platformIds).toContain('facebook');
      expect(platformIds).toContain('netflix');
      expect(platformIds).toContain('github');
      expect(platformIds).toContain('google');
    });

    it('should return 401 for invalid authentication', async () => {
      // Arrange
      const invalidToken = btoa('invalid:credentials');
      setupUserFetch(invalidToken, {}, 401);

      // Act
      const response = await app.request(
        '/connections',
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${invalidToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      expect(response.status).toBe(401);

      // The response should contain error information
      const responseText = await response.text();
      expect(responseText).toContain('Unauthorized');
    });

    it('should show connected state for platforms with active connections', async () => {
      // Arrange
      const userId = `test-user-connected-${testCounter}`;
      const username = 'connecteduser';
      const password = 'testpass';
      const platform: PlatformTypes = 'facebook';

      const basicToken = createUser(username, password, userId);

      // Create and connect a platform
      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const linkResult = await coordinator.createLink(platform, userId);
      await coordinator.markPlatformConnected(linkResult.linkId);

      // Act
      const response = await app.request(
        '/connections',
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as ConnectionsResponse;
      const facebookConnection = responseData.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection).toBeDefined();
      expect(facebookConnection?.connected).toBe(true);

      // Other platforms should still be disconnected
      const otherConnections = responseData.connections.filter((c) => c.id !== 'facebook');
      otherConnections.forEach((connection) => {
        expect(connection.connected).toBe(false);
      });
    });

    it('should return consistent platform metadata', async () => {
      // Arrange
      const userId = `test-user-metadata-${testCounter}`;
      const username = 'metadatauser';
      const password = 'testpass';

      const basicToken = createUser(username, password, userId);

      // Act
      const response = await app.request(
        '/connections',
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as ConnectionsResponse;

      // Verify platform details match constants
      const facebookConnection = responseData.connections.find((c) => c.id === 'facebook');
      expect(facebookConnection).toBeDefined();
      expect(facebookConnection?.name).toBe('Facebook');
      expect(facebookConnection?.logo).toBe('/fb.png');

      const netflixConnection = responseData.connections.find((c) => c.id === 'netflix');
      expect(netflixConnection).toBeDefined();
      expect(netflixConnection?.name).toBe('Netflix');
      expect(netflixConnection?.logo).toBe('/netflix.png');

      const githubConnection = responseData.connections.find((c) => c.id === 'github');
      expect(githubConnection).toBeDefined();
      expect(githubConnection?.name).toBe('Github');
      expect(githubConnection?.logo).toBe('/github.png');
    });
  });

  describe('GET /connections/:platformId', () => {
    it('should return specific platform connection details', async () => {
      // Arrange
      const userId = `test-user-specific-${testCounter}`;
      const username = 'specificuser';
      const password = 'testpass';
      const platformId: PlatformTypes = 'facebook';

      const basicToken = createUser(username, password, userId);

      // Act
      const response = await app.request(
        `/connections/${platformId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as Connection;
      expect(responseData).toBeDefined();
      expect(responseData.id).toBe(platformId);
      expect(responseData.name).toBe('Facebook');
      expect(responseData.logo).toBe('/fb.png');
      expect(typeof responseData.connected).toBe('boolean');
      expect(responseData.connected).toBe(false);
    });

    it('should return 401 for unauthenticated requests', async () => {
      // Arrange
      const platformId: PlatformTypes = 'facebook';
      const invalidToken = btoa('invalid:credentials');
      setupUserFetch(invalidToken, {}, 401);

      // Act
      const response = await app.request(
        `/connections/${platformId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${invalidToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(401);

      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Unauthorized');
    });

    it('should return 500 for invalid platform ID', async () => {
      // Arrange
      const userId = `test-user-invalid-platform-${testCounter}`;
      const username = 'invalidplatformuser';
      const password = 'testpass';
      const invalidPlatformId = 'invalid-platform';

      const basicToken = createUser(username, password, userId);

      // Act
      const response = await app.request(
        `/connections/${invalidPlatformId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(500);

      const responseData = (await response.json()) as any;
      expect(responseData.error).toBe('Internal Server Error');
    });

    it('should show connected state for connected platform', async () => {
      // Arrange
      const userId = `test-user-connected-platform-${testCounter}`;
      const username = 'connectedplatformuser';
      const password = 'testpass';
      const platformId: PlatformTypes = 'github';

      const basicToken = createUser(username, password, userId);

      // Create and connect a platform
      const coordinatorId = env.CoordinatorDO.idFromName(userId);
      const coordinator = env.CoordinatorDO.get(coordinatorId) as DurableObjectStub<CoordinatorDO>;

      const linkResult = await coordinator.createLink(platformId, userId);
      await coordinator.markPlatformConnected(linkResult.linkId);

      // Act
      const response = await app.request(
        `/connections/${platformId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as Connection;
      expect(responseData).toBeDefined();
      expect(responseData.id).toBe(platformId);
      expect(responseData.name).toBe('Github');
      expect(responseData.logo).toBe('/github.png');
      expect(responseData.connected).toBe(true);
    });

    it('should return correct metadata for all supported platforms', async () => {
      // Arrange
      const userId = `test-user-all-platforms-${testCounter}`;
      const username = 'allplatformsuser';
      const password = 'testpass';

      const basicToken = createUser(username, password, userId);

      const testCases: Array<{
        platformId: PlatformTypes;
        expectedName: string;
        expectedLogo: string;
      }> = [
        { platformId: 'facebook', expectedName: 'Facebook', expectedLogo: '/fb.png' },
        { platformId: 'netflix', expectedName: 'Netflix', expectedLogo: '/netflix.png' },
        { platformId: 'github', expectedName: 'Github', expectedLogo: '/github.png' },
        { platformId: 'google', expectedName: 'Google', expectedLogo: '/google.png' },
        { platformId: 'kazeel', expectedName: 'Kazeel', expectedLogo: '/kazeel-logo.png' },
      ];

      // Act & Assert
      for (const testCase of testCases) {
        const response = await app.request(
          `/connections/${testCase.platformId}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Basic ${basicToken}`,
              'Content-Type': 'application/json',
            },
          },
          env,
        );

        expect(response.status).toBe(200);

        const responseData = (await response.json()) as Connection;
        expect(responseData.id).toBe(testCase.platformId);
        expect(responseData.name).toBe(testCase.expectedName);
        expect(responseData.logo).toBe(testCase.expectedLogo);
        expect(typeof responseData.connected).toBe('boolean');
        expect(responseData.connected).toBe(false); // Should be false initially
      }
    });
  });

  describe('DELETE /connections/:platformId', () => {
    it('should disconnect from a platform via DELETE endpoint', async () => {
      // Arrange - Create and connect a platform
      const userId = `test-user-disconnect-${testCounter}`;
      const username = 'disconnectuser';
      const password = 'testpass';
      const platform: PlatformTypes = 'netflix';

      const basicToken = createUser(username, password, userId);

      const coordinator = env.CoordinatorDO.idFromName(userId);
      const coordinatorStub = env.CoordinatorDO.get(coordinator);

      const linkResult = await coordinatorStub.createLink(platform, userId);
      await coordinatorStub.markPlatformConnected(linkResult.linkId);
      await coordinatorStub.updateSessionData(platform, {
        browserState: {
          cookies: [
            {
              name: 'test_cookie',
              value: 'test_value',
              domain: 'example.com',
              path: '/',
              expires: '883612800',
              secure: 'true',
              httpOnly: 'true',
              sameSite: 'Lax',
              size: '25',
              session: 'false',
            },
          ],
          localStorageData: {},
          sessionStorageData: {},
        },
        lastUpdated: Date.now(),
      });

      // Verify platform is connected
      let connectionsResponse = await coordinatorStub.getPlatformConnections();
      let netflixConnection = connectionsResponse.connections.find((c) => c.id === 'netflix');
      expect(netflixConnection?.connected).toBe(true);

      // Act - Disconnect via API
      const response = await app.request(
        `/connections/${platform}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(200);

      const responseData = (await response.json()) as { success: boolean; message: string };
      expect(responseData.success).toBe(true);
      expect(responseData.message).toContain('Successfully disconnected from netflix');

      // Verify platform is now disconnected
      const platforms = (await coordinatorStub.state).platforms;
      const netflixPlatform = platforms.find((c) => c.id === 'netflix');

      expect(netflixPlatform?.connected).toBe(false);
      expect(netflixPlatform?.sessionData).toBe(null);
    });

    it('should return 404 when disconnecting non-connected platform', async () => {
      // Arrange
      const userId = `test-user-disconnect-404-${testCounter}`;
      const username = 'disconnect404user';
      const password = 'testpass';
      const platform: PlatformTypes = 'github';

      const basicToken = createUser(username, password, userId);

      // Act - Attempt to disconnect non-connected platform
      const response = await app.request(
        `/connections/${platform}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Basic ${basicToken}`,
            'Content-Type': 'application/json',
          },
        },
        env,
      );

      // Assert
      expect(response.status).toBe(404);

      const responseData = (await response.json()) as { error: string };
      expect(responseData.error).toBe('Platform not found or not connected');
    });
  });
});

describe('DELETE /connections (all)', () => {
  let localCounter = 0;

  beforeEach(() => {
    localCounter++;
    fetchMock.activate();
  });

  afterEach(() => {
    fetchMock.deactivate();
  });

  it('should delete all connections for authenticated user', async () => {
    // Arrange - Create user and multiple connected platforms
    const userId = `test-user-delete-all-${localCounter}`;
    const username = 'deletealluser';
    const password = 'testpass';
    const basicToken = createUser(username, password, userId);

    const coordinatorId = env.CoordinatorDO.idFromName(userId);
    const coordinatorStub = env.CoordinatorDO.get(
      coordinatorId,
    ) as DurableObjectStub<CoordinatorDO>;

    const platforms: PlatformTypes[] = ['facebook', 'github'];
    const createdLinks: string[] = [];
    for (const p of platforms) {
      const linkResult = await coordinatorStub.createLink(p, userId);
      createdLinks.push(linkResult.linkId);
      await coordinatorStub.markPlatformConnected(linkResult.linkId);
    }

    // Act - Delete all connections via API
    const response = await app.request(
      `/connections`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Basic ${basicToken}`,
          'Content-Type': 'application/json',
        },
      },
      env,
    );

    // Assert
    expect(response.status).toBe(204);

    const coordinatorState = (await coordinatorStub.state) as any;
    expect(Array.isArray(coordinatorState.platforms)).toBe(true);
    expect(coordinatorState.platforms.length).toBe(0);
  });

  it('should return 401 for unauthenticated delete-all request', async () => {
    // Arrange
    const invalidToken = btoa('invalid:credentials');
    setupUserFetch(invalidToken, {}, 401);

    // Act
    const response = await app.request(
      `/connections`,
      {
        method: 'DELETE',
        headers: {
          Authorization: `Basic ${invalidToken}`,
          'Content-Type': 'application/json',
        },
      },
      env,
    );

    // Assert
    expect(response.status).toBe(401);
    const responseData = (await response.json()) as any;
    expect(responseData.error).toBe('Unauthorized');
  });
});
